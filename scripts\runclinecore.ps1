#!/usr/bin/env pwsh
# PowerShell script to install and run cline-core on Windows
# This installs the cline-core app to the user's home directory and starts the service.

$ErrorActionPreference = "Stop"

$CORE_DIR = "$env:USERPROFILE\.cline\core"
$INSTALL_DIR = "$CORE_DIR\0.0.1"
$ZIP_FILE = "standalone.zip"
$ZIP = "dist-standalone\$ZIP_FILE"

Write-Host "Installing cline-core to $INSTALL_DIR..."

# Remove old unpacked versions to force reinstall
if (Test-Path $CORE_DIR) {
    Write-Host "Removing old installation..."
    Remove-Item -Path $CORE_DIR -Recurse -Force -ErrorAction SilentlyContinue
}

# Create installation directory
New-Item -ItemType Directory -Path $INSTALL_DIR -Force | Out-Null

# Check if standalone.zip exists
if (-not (Test-Path $ZIP)) {
    Write-Error "Standalone package not found at $ZIP. Please run 'npm run compile-standalone' first."
    exit 1
}

# Copy and extract the zip file
Write-Host "Copying standalone package..."
Copy-Item $ZIP $INSTALL_DIR

Set-Location $INSTALL_DIR
Write-Host "Extracting package..."
Expand-Archive -Path $ZIP_FILE -DestinationPath . -Force
Remove-Item $ZIP_FILE

# Kill any existing cline-core processes
Write-Host "Stopping any existing cline-core processes..."
Get-Process -Name "node" -ErrorAction SilentlyContinue | Where-Object { $_.CommandLine -like "*cline-core.js*" } | Stop-Process -Force -ErrorAction SilentlyContinue

# Start the service
Write-Host "Starting cline-core service..."
Write-Host "Data directory: $env:USERPROFILE\.cline\data"
Write-Host "Press Ctrl+C to stop the service"
Write-Host ""

$env:NODE_PATH = ".\node_modules"
$env:DEV_WORKSPACE_FOLDER = $env:TEMP
& node cline-core.js
