@echo off
REM Batch script to install and run cline-core on Windows
REM This installs the cline-core app to the user's home directory and starts the service.

setlocal enabledelayedexpansion

set CORE_DIR=%USERPROFILE%\.cline\core
set INSTALL_DIR=%CORE_DIR%\0.0.1
set ZIP_FILE=standalone.zip
set ZIP=dist-standalone\%ZIP_FILE%

echo Installing cline-core to %INSTALL_DIR%...

REM Remove old unpacked versions to force reinstall
if exist "%CORE_DIR%" (
    echo Removing old installation...
    rmdir /s /q "%CORE_DIR%" 2>nul
)

REM Create installation directory
mkdir "%INSTALL_DIR%" 2>nul

REM Check if standalone.zip exists
if not exist "%ZIP%" (
    echo Error: Standalone package not found at %ZIP%
    echo Please run 'npm run compile-standalone' first.
    pause
    exit /b 1
)

REM Copy and extract the zip file
echo Copying standalone package...
copy "%ZIP%" "%INSTALL_DIR%"

cd /d "%INSTALL_DIR%"
echo Extracting package...
powershell -Command "Expand-Archive -Path '%ZIP_FILE%' -DestinationPath '.' -Force"
del "%ZIP_FILE%"

REM Kill any existing cline-core processes
echo Stopping any existing cline-core processes...
taskkill /f /im node.exe 2>nul

REM Start the service
echo Starting cline-core service...
echo Data directory: %USERPROFILE%\.cline\data
echo Press Ctrl+C to stop the service
echo.

set NODE_PATH=.\node_modules
set DEV_WORKSPACE_FOLDER=%TEMP%
node cline-core.js

pause
